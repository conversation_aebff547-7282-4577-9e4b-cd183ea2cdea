#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== DEBUG DATABASE COMPONENT ==="

# Buscar el componente específico
component = Pages::Components::Cols4VariantsV1.find(34)

puts "Componente ID: #{component.id}"
puts "Componente updated_at: #{component.updated_at}"
puts "Componente setup:"
puts component.setup.inspect

puts "\n=== VARIANT IDS EN SETUP ==="
component.setup[:items].each_with_index do |item, index|
  puts "#{index + 1}. variant_id: #{item['variant_id']}"
  
  if item['variant_id'].present?
    begin
      variant = Mkp::Variant.find(item['variant_id'])
      puts "   -> #{variant.title}"
      puts "   -> Product ID: #{variant.product_id}"
    rescue => e
      puts "   -> ERROR: #{e.message}"
    end
  end
end

puts "\n=== FEATURED VARIANTS IDS ==="
store = Mkp::Store.first
component.instance_variable_set(:@store, store)

# Limpiar variables de instancia para forzar recálculo
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)

featured_ids = component.featured_variants_ids
puts "Featured IDs: #{featured_ids}"

puts "\n=== VERIFICAR CADA ID INDIVIDUALMENTE ==="
featured_ids.each_with_index do |id, index|
  begin
    variant = Mkp::Variant.find(id)
    puts "#{index + 1}. ID #{id}: #{variant.title} (Product ID: #{variant.product_id})"
    puts "   Active: #{variant.deleted_at.nil?}, Stock: #{variant.quantity}, Visible: #{variant.visible}"
    puts "   Shop ID: #{variant.shop_id}"
  rescue => e
    puts "#{index + 1}. ID #{id}: ERROR - #{e.message}"
  end
end

puts "\n=== VERIFICAR FIXED VS RANDOM ==="
fixed_ids = component.send(:fixed_variants_ids)
puts "Fixed variants IDs: #{fixed_ids}"
puts "Fixed variants count: #{fixed_ids.length}"

if fixed_ids.length < 8
  puts "⚠️  No hay suficientes fixed variants, se agregarán random variants"
  random_ids = component.send(:random_variants_ids)
  puts "Random variants IDs: #{random_ids}"
  puts "Random variants count: #{random_ids.length}"
end

puts "\n=== VERIFICAR TIENDAS ACTIVAS ==="
active_shop_ids = store.active_shops.pluck(:shop_id)
puts "Store active shop IDs: #{active_shop_ids}"

puts "\n=== VERIFICAR SI LAS VARIANTES CONFIGURADAS PERTENECEN A TIENDAS ACTIVAS ==="
component.setup[:items].each_with_index do |item, index|
  if item['variant_id'].present?
    begin
      variant = Mkp::Variant.find(item['variant_id'])
      belongs_to_active_shop = active_shop_ids.include?(variant.shop_id)
      puts "#{index + 1}. Variant ID #{variant.id}: Shop ID #{variant.shop_id} - Belongs to active shop: #{belongs_to_active_shop}"
    rescue => e
      puts "#{index + 1}. Variant ID #{item['variant_id']}: ERROR - #{e.message}"
    end
  end
end

puts "\n=== FEATURED VARIANTS DETAILS ==="
featured_variants = component.featured_variants(store)
featured_variants.each_with_index do |variant, index|
  puts "#{index + 1}. ID: #{variant.id} - Product ID: #{variant.product_id} - #{variant.title}"
end

puts "\n=== VERIFICAR DUPLICADOS ==="
product_ids = featured_variants.map(&:product_id)
puts "Product IDs: #{product_ids}"
puts "Product IDs únicos: #{product_ids.uniq}"

if product_ids.size != product_ids.uniq.size
  puts "⚠️  HAY PRODUCTOS DUPLICADOS!"
  duplicates = product_ids.group_by(&:itself).select { |k, v| v.size > 1 }
  duplicates.each do |product_id, occurrences|
    puts "   Product ID #{product_id} aparece #{occurrences.size} veces"
    
    # Mostrar qué variantes son del mismo producto
    same_product_variants = featured_variants.select { |v| v.product_id == product_id }
    same_product_variants.each do |variant|
      puts "     - Variant ID #{variant.id}: #{variant.title}"
    end
  end
else
  puts "✅ Todos los productos son únicos"
end

puts "\n=== FIN DEBUG DATABASE ==="
