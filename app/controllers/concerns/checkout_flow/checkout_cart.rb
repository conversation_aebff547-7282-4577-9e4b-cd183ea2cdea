module CheckoutFlow
  class CheckoutCart
    extend CartCookieAndSessionManagement

    attr_reader :address_id,
                :coupon,
                :customer,
                :data,
                :ip,
                :network,
                :payment,
                :purchase_id,
                :taxes,
                :promotion,
                :landing_id,
                :newsletter,
                :store_id,
                :is_pickup,
                :sp_subtotal_points,
                :sp_subtotal_price,
                :choosen_delivery_options,
                :multi_program,
                :points,
                :points_money,
                :points_uuid,
                :biometry_id

    alias_method :payments, :payment

    def initialize(checkout_session)
      @checkout_session = checkout_session
      session = checkout_session.session
      @address_id = session[:address_id]
      @data = session[:data]
      @delivery_option_id = session[:delivery_option_id]
      @delivery_options = session[:delivery_options]
      @ip = session[:ip]
      @network = session[:network]
      @purchase_id = session[:uuid]
      @simple_items = session[:items].presence || []
      @coupon = build_coupon_with_message(session[:coupon])
      @taxes = session[:taxes]
      @newsletter = session[:newsletter]
      @landing_id = session[:landing_id]
      @store_id = session[:store_id]
      @sp_subtotal_points = 0
      @sp_subtotal_price = 0
      @choosen_delivery_options = session[:choosen_delivery_options]
      @warranty_price = session[:warranty_price]
      set_variant_hash
      set_promotion
      subtotal_points
      @multi_program = session[:multi_program]
      @points = session[:points]
      @points_money = session[:points_money]
      @points_uuid = session[:points_uuid]
      @biometry_id = session[:biometry_id]
    end

    def customer
      @customer ||= @checkout_session.customer
    end

    def customer_uuid
      customer.uuid
    end

    def store
      @story ||= Mkp::Store.find_by_id(@store_id)
    end

    def customer_address
      @checkout_session.session[:address_id] = address_id if @checkout_session.session[:address_id].blank?
      return unless @checkout_session.session[:address_id]
      customer.addresses.find_by_id(@checkout_session.session[:address_id]) if customer.present?
    end

    def payment
      @payment ||= @checkout_session.payment
    end

    def balance_due?
      total > 0
    end

    def parsed_total
      sprintf "%.2f", total
    end

    def items_per_shop
      checkout_items.group_by { |item| item.shop }
    end

    def only_points?(params)
      params[:gateway] == 'decidir' && params.dig(:response, :id).blank? && @sp_subtotal_price == 0
    end

    def matrix_operator?
      @delivery_options.keys.include?(:matrix)
    end

    def title
      @title ||= Mkp::PurchaseTitleizer.perform(
        checkout_variants.first.try(:title),
        checkout_variants.size,
        network
      )
    end

    def total
      (subtotal + shipping_cost + taxes_cost - coupon_total_discount - promotion_discount).round(2)
    end

    def total_condition?(t)
      (@sp_subtotal_points == 0 && t > 0) || @sp_subtotal_price > 0
    end

    def total_one_installment
      return discount_installment unless store.percentage_fee.zero?
      total
    end

    def discount_installment
      (total - percentage_fee).round(2)
    end

    def percentage_fee
      total * store.percentage_fee.fdiv(100)
    end

    def subtotal
      subtotal_price = 0

      checkout_items.each do |item|
        points_value = item.product.points_value_by_store(store, item.points)
        subtotal_price += item.total - points_value
      end

      subtotal_price > 0 ? subtotal_price : 0
    end

    def total_points
      points = user_points - @sp_subtotal_points
      shipping_cost = shipping_cost_points
      ship = shipping_cost if (points > 0) && (points >= shipping_cost) #total con puntos es sufuciente
      @sp_subtotal_points + ( ship || 0.0)
    end

    def shipping_cost
      has_points = checkout_items.any? { |item| item.product.transaction_type == 'points' }
      Rails.logger.info "=== SHIPPING_COST: has_points=#{has_points}, choosen_delivery_options=#{choosen_delivery_options} ==="
      return 0 if has_points
      return 0 unless choosen_delivery_options.present?
      choosen_delivery_options.sum{|key, value| value.sum(&:charge)}
    end

    def shipping_cost_points
      (shipping_cost * store.equivalent_points).to_i
    end

    def choose_shipping_cost
      points = user_points - @sp_subtotal_points
      shipping_cost = shipping_cost_points
      return self.shipping_cost unless (points > 0) && (points >= shipping_cost)
      0.0
    end

    def taxes_cost
      taxes
    end

    def coupon_items_discount
      coupon.try(:[], :discount) || 0.0
    end

    def set_discount(discount)
      return if checkout_items.blank?

      highest_value_item_price = checkout_items.map(&:product).max_by(&:price).price || 0.0
      diff = highest_value_item_price  - discount
      discount = diff.negative? ? highest_value_item_price : discount
      coupon[:discount] = discount if coupon.present? && coupon[:discount].blank?
    end

    def coupon_total_discount
      return 0.0 unless coupon.present?
      coupon_items_discount
    end

    def coupon_available_amount
      coupon[:amount] - coupon_items_discount
    end

    def promotion_discount
      promotion.try(:[], :discount) || 0.0
    end

    def items
      @items ||= self.class.send(:extract_relevant_data, @simple_items)
    end

    def is_pickeable?
      shop_id = items.first[:product][:shop][:id]
      items.all? {|item| item[:product][:pickeable] && item[:product][:shop][:id] == shop_id}
    end

    def remove_coupon
      @checkout_session.remove_coupon
      @coupon = nil
    end

    def taxes(destination_state = nil, shop = nil)
      state = destination_state || (customer && customer_address && customer_address.state)

      return 0 unless state.present?

      items = if shop.present?
        checkout_items.select { |item| item.shop == shop }
      else
        checkout_items
      end

      items.map { |item| item.tax(state) }.sum.to_f.round(2)
    end

    def shops
      @shops ||= checkout_items.map { |item| item.variant.shop }.uniq
    end

    def zones
      @zones ||= shops.flat_map(&:zones)
    end

    def countries
      @countries ||= zones.flat_map do |zone|
        zone.countries.presence || ['ALL']
      end.uniq
    end

    def shipments
      checkout_items.group_by { |item| item.variant.shop.fulfillment_shop }
    end

    def checkout_items
      @checkout_items = checkout_variants.map do |variant|
        if variant.product.warranty? && @warranty_price.present?
          CheckoutWarrantyItem.new(variant, @variant_hash[variant.id][:quantity], @warranty_price, @variant_hash[variant.id][:points])
        else
          CheckoutItem.new(variant, @variant_hash[variant.id][:quantity], @variant_hash[variant.id][:points])
        end
      end
    end

    # TODO: We should discuss where is the best place for this logic.
    def has_shipment_bonification?
      state = customer_address.try(:[], :state)
      eligible_total = (subtotal - coupon_total_discount - promotion_discount)
      free_shipping_from = Network[network].free_shipping_from
      furniture_ids = get_furnitures_ids
      categories_ids = checkout_items.map{ |item| item.product.category_id }
      (eligible_total >= free_shipping_from && store.try(:inherit_free_shipping) && (furniture_ids & categories_ids).empty? )
      #(eligible_total >= free_shipping_from && store.try(:inherit_free_shipping)) && (state == "Capital Federal" || state == "GBA") && (furniture_ids & categories_ids).empty?
      # return false if Mkp::DiscountHandler.applied?(self)
    end

    def has_shipment_promotion?
      get_promotion_instance.get_shipping_promotion.present?
    end

    def get_promotion
      {
        name: (promotion && promotion[:display_name]) || '',
        discount: (promotion && promotion[:discount]) || 0
      }
    end

    def get_promotion_instance
      @promotion_instance ||= Mkp::PromotionService.new(self)
    end

    def delivery_options=(delivery_options)
      @delivery_options = delivery_options
    end

    def delivery_options
      @delivery_options
    end

    def address_id=(address_id)
      @address_id = address_id
    end

    def delivery_option_id=(delivery_option_id)
      @delivery_option_id = delivery_option_id
    end

    def delivery_option_id
      @delivery_option_id
    end

    def max_installments
      # we assume that if nil, there is no limit for the installments
      checkout_variants.joins(product: {category: :category_stores})
                .where("mkp_category_stores.store_id": @store_id)
                .pluck(:installments).compact.map(&:to_i).min || 50
    rescue
      50
    end

    def user_points
      @user_points ||= (customer.try(:points) rescue 0) || 0
    end

    def insurance_option_for_items?
      store.strategy_insurances(self).enabled_for_items?
    end

    def insurance_cost_by_currency
      store.strategy_insurances(self).cost_by_currency
    end

    def extra_info
      store.strategy_insurances(self).extra_info checkout_items
    end

    def insurance_candidates
      store.strategy_insurances(self).candidates
    end

    def own_gateways?(doc_number = nil)
      return false if !(customer || doc_number)
      strategy_gateway_installments = store.strategy_gateway_installments
      document_number = doc_number || customer.doc_number || customer.addresses.last.doc_number
      strategy_gateway_installments.own_gateways? && !strategy_gateway_installments.own_gateways_for(Gateways::Criteria::FirstData.new(document_number: doc_number, store: store, items: checkout_items)).empty?
    end

    def own_gateways
      store.strategy_gateway_installments.own_gateways
    end

    def external_program_id
      return DEFAULT_EXTERNAL_PROGRAM_ID if multi_program?

      external_program_ids.last
    end

    def multi_program?
      external_program_ids.size > 1
    end

    def full_points?
      checkout_items.all? { |item| item.product.transaction_type == 'points' }
    end

    private

    def external_program_ids
      @external_program_ids ||= checkout_items.flat_map do |item|
        item.shop
          .shop_stores
          .where(store: store)
          .joins(:payment_program)
          .pluck(:external_program_id)
          .reject{ |external_program_id| external_program_id == OCHENTA_EXTERNAL_PROGRAM_ID }
      end.uniq
    end

    def subtotal_points
      @sp_subtotal_points = 0 if @sp_subtotal_points.nil?
      subtotal_price = 0

      checkout_items.each do |item|
        points_price = item.product.points_price || 0
        @sp_subtotal_points += points_price
      end

      @sp_subtotal_points
    end

    def set_variant_hash
      @variant_hash = @simple_items.each_with_object({}) do |item, hash|
        hash[item[:variant_id]] = { quantity: item[:quantity], points: item[:points] }
      end
    end

    def set_promotion
      @promotion = Mkp::PromotionService.new(self).applied_promotion
    end

    def checkout_variants
      Mkp::Variant.active.with_stock.where(id: @variant_hash.keys)
    end

    def get_furnitures_ids
      mattresses_ids = Mkp::Category.find(746).descendants.map(&:id) << 746
      #furnitures_ids = Mkp::Category.find(840).descendants.map(&:id) << 840
      couches_ids = Mkp::Category.find(905).descendants.map(&:id) << 905
      mattresses_ids + couches_ids
    rescue ActiveRecord::RecordNotFound
      []
    end

    def build_coupon_with_message(coupon)
      return if coupon.blank?

      return coupon if items.nil?

      return coupon.merge!(msg: I18n.t('v5.controllers.checkout.warning.message')) if items.size > 1

      coupon
    end
  end
end
