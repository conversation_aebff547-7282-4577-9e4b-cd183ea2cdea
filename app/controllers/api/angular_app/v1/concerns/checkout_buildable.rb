module Api::AngularApp::V1::Concerns::CheckoutBuildable
  extend ActiveSupport::Concern

  def build_cart
    params[:items] ||= []

    if @cart.present? # Should we remove item if param is empty? && params[:items].present?
      @cart.items.destroy_all
      @cart.update_items(params[:items])
    end

    if @cart.nil?
      @cart = @current_store.carts.create({
        network: @network,
        status: 'checkout'
      }.tap do |cart|
        if @current_user
          cart[:customer_id] = @current_user.id
          cart[:customer_type] = @current_user.class.name
        end
      end)
      @cart.update_items(params[:items])
    end
    validate_purchase_store
  end

  def build_checkout_cart
    return cart_not_found if @cart.nil?

    @checkout_session = ::CheckoutFlow::CheckoutSession.new(
      {
        store_id: @current_store.id,
        items: @cart.items_to_checkout,
        coupon: nil,
        network: @network,
        address_id: verified_id_address(@cart.address_id),
        delivery_option_id: @cart.delivery_option_id,
        is_pickup: @cart.is_pickup,
        newsletter: @cart.newsletter,
        choosen_delivery_options: @cart.get_selected_delivery_options,
        uuid: @cart.purchase_id,
        ip: real_remote_ip,
        warranty_price: @warranty_price,
        data: { associated_variant: @associated_variant },
        points: @cart.points,
        points_money: @cart.points_money,
        points_uuid: @cart.points_uuid,
        biometry_id: @cart.biometry_id
      }.tap do |whitelist|
        if @current_user
          whitelist[:customer] = {id: @current_user.id, type: 'Customer'}
          whitelist[:address_id] = @current_user.addresses.find_by_id(@cart.address_id).present? ? verified_id_address(@cart.address_id) : nil
        elsif @cart.customer.try(:id).present?
          whitelist[:customer] = {id: @cart.customer.try(:id), type: @cart.customer_type.demodulize.capitalize }
          whitelist[:address_id] = @cart.customer.addresses.find_by_id(@cart.address_id).present? ? @cart.address_id : nil
        end
        if @cart.coupon.present?
          whitelist[:coupon] = {
            id: @cart.coupon.id,
            code: @cart.coupon.code
          }
          if @cart.coupon.percent_policy?
            whitelist[:coupon][:percent] = @cart.coupon.percent
          else
            whitelist[:coupon][:amount] = @cart.coupon.amount
          end
        end
      end
    )

    Rails.logger.info("Checkout Session: #{@checkout_session}")
    @checkout_cart = ::CheckoutFlow::CheckoutCart.new(@checkout_session)
    discount = ::Mkp::DiscountHandler.determine_amount_for(@checkout_cart)
    @discount = @checkout_cart.set_discount(discount)
  end

  def build_coupon
    params[:coupon_code] ||= ''

    coupon = @current_store.coupons.where('lower(code) = ?', params[:coupon_code].downcase).last

    if coupon.present? && ::Mkp::DiscountHandler.perform!(:apply, @checkout_cart, coupon)
      @cart.coupon = coupon
      @cart.save!
      build_checkout_cart # There is a explicitly need of build the checkout cart again
      render :cart
    else
      render json: {error: 'Invalid coupon'}, status: 200
    end
  end

  def build_address(address_params, address_id, billing_address)
    user = @current_user.presence || @cart.customer || build_guest(address_params)
    params = address_parameters(address_params, billing_address) if address_params
    address = user.addresses.find_by_id(address_id) if address_id
    address.update(params) if address && params
    address = address || user.addresses.find_or_create_by(params)
    @cart.customer = user
    @cart.address = address
    @cart.save!
  end

  def address_parameters(address_params, billing_address)
    {
      first_name: address_params[:name].split.first,
      last_name: address_params[:name].split.drop(1).join(' '),
      telephone: address_params[:phone],
      address: address_params[:address],
      address_2: address_params[:depth],
      street_number: address_params[:street_number],
      city: address_params[:city],
      state: address_params[:state],
      country: 'AR',
      zip: address_params[:postal_code],
      doc_type: address_params[:doc_type],
      doc_number: address_params[:doc_number],
      billing_address: OpenStruct.new(billing_address)
    }
  end

  def build_guest(address_params)
    ::Mkp::Guest.create(
      first_name: address_params[:name].split.first,
      last_name: address_params[:name].split.last,
      email: address_params[:email].downcase,
      doc_number: address_params[:doc_number],
      network: @network
    )
  end

  def build_delivery_options
    return if @cart.address.nil?

    @cart.delivery_options.destroy_all if @cart.delivery_options.any?
    @cart.build_delivery_options(@checkout_cart)
  end

  def build_gateway_params
    param = set_payment_params
    params[:payment] = param if param.present?
    Rails.logger.info(param)
    Rails.logger.info(params)
  end

  def validate_purchase_store
    if @cart.store_id.nil? || @cart.status.nil?
      @cart.update(store_id: @current_store.id, status: "checkout")
    end
  end

  def build_loyalty_program_discount(params)
    return unless params[:points_type].present? && params[:points_discount].present? && params[:points_discount].to_i != 0
    raise StandardError.new('El sistema de puntos esta desactivado') unless LoyaltyConfig.active
    raise StandardError.new('El sistema de puntos esta desactivado para el usuario') unless @current_user.points_enabled?
    raise StandardError.new('El usuario no tiene DNI registrado') unless @current_user.doc_number.present?

    allowed_percentages_for_points = ['0', '25', '50', '75']
    allowed_percentages_for_points << '100' if @cart.full_points?
    raise StandardError.new('Parametros de puntos no permitidos') unless params[:points_type] == 'percentage' && params[:points_discount].in?(allowed_percentages_for_points)

    amount = 0
    forced_points = nil
    if params[:points_type] == 'percentage'
      if params[:points_discount] == '100'
        forced_points =  @cart.checkout_items.map {|i| i.product.points_price }.sum
        amount = @cart.total
      else
        amount = (@cart.total * params[:points_discount].to_f / 100.0).round(0)
      end
    end
    return unless amount > 0

    current_month_points = Mkp::Payment.select(:gateway_data).where(gateway: "LoyaltyBna", status: "collected", collected_at: [Time.now.at_beginning_of_month...Time.now]).map{|p| p.gateway_data['points'] || 0}.sum
    raise StandardError.new('El monto de puntos a descontar supera el limite mensual') if current_month_points > LoyaltyConfig.max_amount_per_month

    begin
      user_points_money = @current_user.points_to_money(with_cache: false)
    rescue Loyalty::ApiExceptions::ApiExceptionError => e
      raise Loyalty::ApiExceptions::ApiExceptionError.new("En este momento no podemos procesar el pago con puntos. Podes abonar la compra con los medios habilitados.")
    end

    raise StandardError.new('El usuario no tiene suficientes puntos') if user_points_money < amount
    @cart.points_uuid = SecureRandom.uuid
    @cart.points_money = amount
    @cart.points = forced_points || @current_user.money_to_points(amount)

    raise StandardError.new('El monto de puntos a descontar supera el limite mensual') if current_month_points + @cart.points > LoyaltyConfig.max_amount_per_month * BNA_LOYALTY_POINTS_OVER_PERC

    @cart.save!
  end

  private

  def verified_id_address(address_id)
    return [] if address_id.nil?
    return [] unless Mkp::Address.find(@cart.address_id).new_address
    address_id
  end
end
