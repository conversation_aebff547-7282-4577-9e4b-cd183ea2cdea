# frozen_string_literal: true
require 'net/http'

module Api
  module AngularApp
    module V1
      # Controller for checkout
      class InvalidGatewayException < StandardError; end

      class CheckoutController < ApplicationController
        class IdentityValidationTokenException < StandardError; end
        class CreateOrderException < StandardError; end
        class InsuranceError < StandardError; end
        module CheckoutCart
          class Error < StandardError; end
        end

        class ProductAvailabilityException < StandardError; end

        rescue_from Avenida::Payments::InvalidAmount, IdentityValidationTokenException, InvalidGatewayException, CreateOrderException, CheckoutCart::Error, ProductAvailabilityException, InsuranceError do |exception|
          render_error(exception.message)
        end

        include CheckoutFlow
        include Api::AngularApp::V1::Concerns::ExtendedWarranty
        include Api::AngularApp::V1::Concerns::PaymentBuildParams
        include Api::AngularApp::V1::Concerns::CheckoutBuildable
        include Api::AngularApp::V1::Concerns::TodopagoProcessable
        include Api::AngularApp::V1::Concerns::LoggerTrace
        include Api::AngularApp::V1::Concerns::CheckoutAuthenticable
        include Api::AngularApp::V1::Concerns::IntegrationServices
        include Api::AngularApp::V1::Concerns::RenaperVerifiable
        include Api::AngularApp::V1::Concerns::CheckoutPendingPayment

        include CheckoutProcessable

        rescue_from Api::AngularApp::V1::Concerns::CheckoutAuthenticable::UnauthorizedError do |exception|
          invalid_authentication
        end

        before_filter :authenticate_user!, :find_cart, only: %i[
          init cart_quantity coupon authorize_todopago withdrawal_branch
          remove_coupon address delivery_options payment
          subscription_newsletter first_purchase installments
          installments_no_bines jubilo_credit_request read_summary extended_warranties check_customer_apply_to_loan get_loan_options ask_loan
          loan_form
        ]

        # POST extended_warranties no tiene header auth_token
        skip_before_filter :authenticate_user!, only: %i[extended_warranties]

        before_filter :ensure_cart_is_there, except: %i[
          init cart_quantity done first_purchase
          payment_intent
        ]
        before_filter :validate_variant_params, only: [:init]

        before_filter :find_associated_variant, :find_warranty_variant

        def init
          service = Checkout::ValidateProductAvailability.new(params[:items],
                                                              @current_store)
          service.perform

          if service.valid
            build_cart

            build_checkout_cart
            render :cart
          else
            render json: { error: service.error }, status: 406
          end
        end

        def cart_quantity
          if @cart.nil?
            render json: { quantity: 0 }, status: 200
          else
            render json: { quantity: @cart.items.count }, status: 200
          end
        end

        def authorize_todopago
          build_checkout_cart
          build_todopago_vars
          operation = build_todopago_operation(@cart,
                                               @checkout_cart,
                                               @current_store)

          response = @connector.sendAuthorizeRequest(@commerce, operation)
          logger_trace('todopago', response, operation)
          response = JSON.parse(response)['envelope']['body']['send_authorize_request_response']

          if response['status_code'].to_i == -1
            @cart.update_attribute(:data, { todopago: response })
            render json: response.to_json
          else
            render json: { error: 'Something went wrong, please try again' }
          end
        end

        def coupon
          build_checkout_cart
          build_coupon
        rescue ::Mkp::Coupon::CouponExpiredError => e
          render json: { error: 'El cupón ha expirado' }
        rescue ::Mkp::Coupon::CouponNotActiveError => e
          render json: { error: 'Este cupón no se encuentra activo' }
        rescue ::Mkp::Coupon::UserLimitedReachedError => e
          render json: { error: 'Has alcanzado el máximo de usos para este cupón' }
        rescue ::Mkp::Coupon::BelowAmountLimitError => e
          render json: { error: t('mkp.checkout.coupons_error.below_amount_limit') }
        rescue ::Mkp::Coupon::InvalidCategoryError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_category', name: e.data[:name]) }
        rescue ::Mkp::Coupon::CouponNotOnsaleError => e
          render json: { error: t('mkp.checkout.coupons_error.on_sale') }
        rescue ::Mkp::Coupon::InvalidManufacturerError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_manufacturer', name: e.data[:name]) }
        rescue ::Mkp::Coupon::InvalidShopError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_shop') }
        rescue ::Mkp::Coupon::InvalidProductError => e
          render json: { error: t('mkp.checkout.coupons_error.invalid_product', name: e.data[:name]) }
        end

        def remove_coupon
          @cart.update_attribute(:coupon_id, nil)
          build_checkout_cart

          render :cart
        end

        def address
          @cart.update_attribute(:is_pickup, params[:is_pickup])
          build_address(params[:form], params[:address_id], params[:billing_address])
          build_checkout_cart
          build_delivery_options

          render :cart
        end

        def delivery_options
          @cart.delivery_options.each do |option|
            option.mark_unselected! if option.selected
          end
          if params[:delivery_option_ids].present?
            params[:delivery_option_ids].each do |selected_option|
              cart_option = @cart.delivery_options.find(selected_option['cart_delivery_option_id'])
              @cart.update!(is_pickup: cart_option.pickup)
              cart_option.mark_selected! if cart_option.present?
            end
          end

          @cart.delivery_options.reload
          build_checkout_cart

          render :cart
        end

        def check_customer_apply_to_loan
          return render json: { message: -1 } if !@current_user.cuit.present?
          return render json: { message: -1 } if @cart.total < MIN_LOAN_AMOUNT
          cuit = @current_user.cuit
          url = URI(COYOTE_URL_PRESTAMOS + "/payments/loans/feasibility/#{cuit}")

          response = Net::HTTP.get_response(url)
          if response.is_a?(Net::HTTPSuccess)
            body = JSON.parse(response.body)
            if body['response'] == 0
              if body['maxAmount'] >  @cart.total
                @cart.loan_max_amount = body['maxAmount'].to_d
                @cart.save!
                session[:bna] = body['offerCode']
                return render json: { message: body['response'], offer_code: body['offerCode'] }
              else
                return render json: { message: -1 }
              end
            else
              return render json: { message: body['response'] }
            end
          else
            render json: { error: "Error: #{response.message}" }, status: response.code.to_i
          end
        end

        def get_loan_options
          cuit = @current_user.cuit
          offer_code = nil
          offer_code = params[:offer_code] if params[:offer_code].present?
          product_code = offer_code.present? ? offer_code : nil
          return render json: { error: "Error, falta producto" }, status: 400 if product_code.nil?
          url = URI(COYOTE_URL_PRESTAMOS + "/payments/loans/loanInquiry/#{cuit}/#{@cart.purchase_id}/#{product_code}/#{@cart.total}")
          response = Net::HTTP.get_response(url)
          if response.is_a?(Net::HTTPSuccess)
            body = JSON.parse(response.body)
            session[:bna_offers] = body
            if body['response'] == 0 && body['maxAmount'] >=  @cart.total
              body['maxAmount'] = @cart.loan_max_amount
              return render json: { body: body }
            end
          end
          render json: { error: "Error" }, status: 400
        end

        def installments
          @installments = @current_store.installments_for(@cart, params[:bin], params[:doc_type], params[:doc_number], @current_store.use_bines)
          # @customer_gateway = @installments.first&.gateway
          @installment_by_amount = params[:amount] || @cart.total
          @installment_by_amount = @installment_by_amount.to_f
          @installments = installments_array_details(@installments, @installment_by_amount)
          @customer_gateway = @current_store.strategy_gateway_installments.own_gateways_for(
            Gateways::Criteria::FirstData.new(
                  document_number: params[:doc_number],
                  store: @current_store,
                  items: @cart.checkout_items)).first
          render_installments_error('Tarjeta no válida') unless @installments.any?
        end

        def installments_array_details(installments, amount = nil)
          total_amount = amount || @cart.total
          installments_details = installments.map { |i| [ i.number, i.coef.to_f, sprintf('%.2f', i.total_by_installment(total_amount)), i.government_program] }
          payment_program_count = installments.map(&:payment_program_id).uniq.count
          installments_details  = installments_details.group_by(&:itself).map { |k,v| [k, v.count] }.to_h.map{ |key,value| key if value == payment_program_count}.compact
          filtered_installments = []
          installments_details.each do |ins|
            installments.each do |i|
              if i.number == ins[0] && i.coef.to_f == ins[1] && sprintf('%.2f', i.total_by_installment(total_amount)) == ins[2] && i.government_program == ins[3]
                filtered_installments << i
                break
              end
            end
          end
          filtered_installments
        end

        def subscription_newsletter
          @cart.update_attribute(:newsletter, params[:newsletter])
          build_checkout_cart
          render :cart
        end

        def withdrawal_branch
          zip = @cart.address.try(:zip) || '1614'
          @branch_offices = OcaService.branch_offices(zip.to_i)
        end

        def payment
          check_identity_response!

          if !first_purchase_check(@current_store, params[:doc_number] || params[:card_holder_identification][:number])
            render json: { success: false, message: 'Error al validar número de documento' }, status: 403
            return
          end

          @insurance_data = nil
          set_tdd_boca_address
          build_loyalty_program_discount(params)
          build_checkout_cart

          service = Checkout::ValidateProductAvailability.new(@cart.items_to_checkout, @current_store)
          service.perform

          Rails.logger.info("<Checkout Controller> Items to Checkout: #{@cart.items_to_checkout}")
          if service.valid

            begin
              ActiveRecord::Base.transaction do
                if proccess_points_payment
                  # For points products, skip delivery options check since shipping is free
                  has_points_products = @checkout_cart.checkout_items.any? { |item| item.product.transaction_type == 'points' }
                  delivery_options_ok = has_points_products || @checkout_cart.choosen_delivery_options.present?

                  if @checkout_cart.total >= 0 && delivery_options_ok
                    build_gateway_params
                    process_payment_angular if params[:payment].present?
                    Rails.logger.info("<Checkout Controller> Checkout Cart Items: #{@checkout_cart.items}")
                    @array_insurance_data = Hash.new
                    if @checkout_cart.items.count > 0 && ::Mkp::Order.find_by_purchase_id(params['purchase_id']).blank?
                      # Skip insurance processing for points products
                      has_points_products = @checkout_cart.checkout_items.any? { |item| item.product.transaction_type == 'points' }
                      if insurance_enabled? && insurance_checked_by_user? && !has_points_products
                        checkout_cart_strategy_insurance.candidates.each do |item|
                          item.quantity.times do |i|
                            @insurance_data = new_insurance_data(insurance_params, item)
                            raise InsuranceError, "Revise los datos del seguro", [] if !@insurance_data.valid?
                            @array_insurance_data.merge!({@insurance_data => item})
                          end
                        end
                      end

                    collect_payment
                    end
                  end

                  logger_trace('checkout')
                  # For points products, skip delivery options check since shipping is free
                  has_points_products = @checkout_cart.checkout_items.any? { |item| item.product.transaction_type == 'points' }
                  delivery_options_missing = !has_points_products && @checkout_cart.choosen_delivery_options.blank?

                  if (@checkout_cart.balance_due? && ( @payment.blank? || @payment.any? { |payment| payment.cancelled? })) || delivery_options_missing
                    if @points_payments.present?
                      @points_payments.each { |payment| payment.cancel!(@current_store) }
                    end

                    cancelled_payment = @payment&.detect(&:cancelled?)
                    cancel_existing_payments

                    # error = @payment&.cancelled? ? get_payment_error_message : missing_payment_error_message
                    error = cancelled_payment.present? ? get_payment_error_message(cancelled_payment) : missing_payment_error_message
                    if delivery_options_missing
                      error = 'Debes elegir opciones de envío'
                    end

                    if @payment.present? && @payment.any? { |payment| payment.get_error[:status] == 'gateway_error' }
                      ExceptionNotifier.notify_exception(StandardError.new, data: {
                          error: error,
                          payment: @payment || '',
                          checkout_cart: @checkout_cart,
                          params: params.dup,
                          session: session.dup
                      })
                    end

                    render_error(error)
                  else
                    insurance_tokens = Hash.new
                    create_order(@current_store.id)
                    if !@array_insurance_data.empty?
                      @array_insurance_data.each do |insurance, item|
                        new_insurance_token = save_insurance(insurance)  #save the result at service
                        if !new_insurance_token
                          cancel_existing_payments
                          render_error('No se pudo procesar el seguro. Intente mas tarde') and return
                        end
                        insurance_tokens.merge!({new_insurance_token => item})
                      end
                    end
                    # Skip insurance token creation for points products
                    unless @checkout_cart.checkout_items.any? { |item| item.product.transaction_type == 'points' }
                      insurance_tokens.each do |insurance_token, item|
                        create_insurance_token(insurance_token, item)
                      end
                    end
                    create_and_associate_shipments

                    warranty_request('Boston') if warranty?

                    first_purchase_register
                    run_integration_services(:create)
                    if @current_store.name == 'bancomacro'
                      ::Mkp::Integration::PuntosYPremios.new.redemption(@order, nil)
                    end
                    suborders_coupon_generator if @order.coupon.present?
                    # if params[:is_pickit] == '1'
                    #   confirm_pickit_order(params[:pickit_quotation_id])
                    # end
                    remove_current_cart(params[:gateway]) if @cart.present?
                    set_gross_total
                    render json: response_payment
                  end
                else
                  render_error('No se pudo procesar el pago con puntos')
                end
              end
            rescue Avenida::Payments::InvalidAmount, InsuranceError
              raise
            rescue => e
              Rails.logger.error e
              if @payment.present?
                @payment.each do |payment|
                  next if payment.cancelled?
                  "Avenida::Payments::#{payment.gateway}".constantize.cancel_payment_by_store!(payment.gateway_object_id, @current_store, payment, @checkout_cart)
                  # modo_logger.info("cancel_payment_by_store: #{payment.gateway_object_id}")
                end
              end

              if params[:gateway] == 'loan'
                # Mensaje especial para préstamos
                log_payment_fail(@payment, @checkout_cart.items, e)
                message = e.message.presence || "Ha ocurrido un error que no permite avanzar en el pago de la compra.\n\nIntentá realizar la compra con préstamo más tarde o abonar ahora con otro medio de pago."
                raise CreateOrderException.new(message)
              else
                # Mensaje genérico para el resto
                log_payment_fail(@payment, @checkout_cart.items, e)
                raise CreateOrderException.new('La transacción no pudo ser completada, cancelando pagos')
              end
            end
          else
            render_error(service.error)
          end
        end

        def loan_form
          service = QrService.new
          token = service.fetch_access_token
          session_id = service.get_session_id_key(token)
          # PROD
          #session_id = service.get_session_id_key(token, 'acc6116b-bf9f-47d5-b6ba-830677d25524')
          @loan_form_data = service.get_form_data_for_qr(token_id: session_id, dni: params[:dni], sexo: params[:sexo], externaltxid: "#{@cart.purchase_id}*#{request.headers['Auth-Token']}")
        end

        def warranty?
          @order.shops.detect { |shop| shop.id == OCHENTA_SHOP_ID }
        end

        def warranty_request(provider)
          ::Mkp::Warranties::WarrantyRequestWorker.perform_in(10.seconds, @order.id, provider)
        end

        def revert_order
          # Borra una orden que acaba de ser creada
          motives = {
              type: 'Dinero en TC',
              reason: 'Producto faltante'
          }
          service = Cancelation::OrderCancelationService.new(order: @order, items: @order.items.map(&:id), motive: motives)
          @order.destroy if service.cancel
        end

        def done
          response = Tdigital::CheckoutProcessor.new(purchase_id: params[:purchase_id]).done
          render json: response.body, status: response.code
        end


        def first_purchase
          case first_purchase_validate(store: @current_store, document_number: params[:document_number])
          when FirstPurchase::Validator::SUCCESS
            render json: { success: true, message: "Ok" }, status: :ok
          when FirstPurchase::Validator::WHITELIST_FAILURE
            render json: { success: false, message: "Estimado cliente, tu DNI no se encuentra habilitado para esta promoción especial. Te redirigiremos a la tienda para que puedas disfrutar todas nuestras promociones y beneficios. ¡Muchas gracias!" }, status: :bad_request
          when FirstPurchase::Validator::FIRST_PURCHASE_FAILURE
            first_purchase_msg = "Estimado cliente, esta promoción especial solo permite una compra por usuario, verificamos que ya adquiriste un producto."
            if (@current_store.id == 47)
              first_purchase_msg += " En caso de no haber realizado la compra contactate con nosotros haciendo click <a href='https://www.tiendabna.com.ar/ayuda'>aquí</a> para que podamos ayudarte. ¡Muchas gracias!"
            end
            render json: { success: false, message: first_purchase_msg }, status: :unauthorized
          else
            render json: { success: false, message: "Service failure."}, status: :forbidden
          end
        end

        def first_purchase_validate(args)
          FirstPurchase::Validator.call(
            store: args[:store],
            document_number: args[:document_number],
            first_purchase_authorization: FirstPurchase::Criteria::Blacklist.new(
               store: args[:store],
               document_number: args[:document_number],
               document_type: nil
            )
          )
        end

        def jubilo_credit_request
          if @current_store.name == 'jubilostaging' || @current_store.name == 'jubilo'
            Mkp::AbandonedCartMailer.jubilo_credit_request(jubilo_credit_params, @cart).deliver

            render json: { success: true }, status: :ok
          else
            render json: { success: false, message: 'Servicio no disponible para esta tienda' }, status: 403
          end
        end

        def read_summary
          build_checkout_cart
          installments = params[:installments]
          total_with_interests = @checkout_cart.total

          if installments.present?
            total_with_interests = installments.sum do |installment|
              installment[:installments_number] * installment[:total_by_installment]
            end
          end
          @total_with_interests = total_with_interests
          @interests = (@total_with_interests - @checkout_cart.total).abs.to_f
          render :summary
        end

        def extended_warranties
          # Map categories and products in the cart
          cart_product_mapper = Ochenta::Category::Mapper::CartProduct.new(cart: @cart)

          # Get warranties
          body_request = Ochenta::Request.new(habilitadas: true, category_ids: cart_product_mapper.category_ids).build
          response = Ochenta::Coverages::WarrantiesService.call(body_request: body_request)

          if response&.success?
            parser = Ochenta::Coverages::CategoryWarrantiesParser.new(response.payload)
            category_warranties = parser.category_warranties.select(&:any?)
            external_coef_mapper = Ochenta::Category::Mapper::ExternalCoef.new(category_warranties: category_warranties)

            category_ids = category_warranties.map(&:category_id)
            response = Ochenta::Cart::Warranties.new(
              mapper_instance: cart_product_mapper,
              available_categories: category_ids,
            ).detector

            if response&.success?

              @associated_variant = ::Mkp::Variant.find(response.payload[:variant_id])

              return nil if not_applicable?

              @associated_product = @associated_variant.product

              @coefficients = response.payload[:category_ids].flat_map{|category_id| external_coef_mapper.fetch(category_id)}
                                      .each_with_object({}){|coef, obj| obj[coef.coverage_id] = coef.coefficient }

              variants = ::Mkp::Variant.where('properties LIKE ?', '%external%').select{|v| @coefficients.keys.include? v.properties[:external]}

              product_ids = variants.flat_map(&:product).map(&:id)

              @entities = ::Mkp::Product.by_transaction_type(5).where(id: product_ids)
            end
          end
        end

        private

        def cancel_existing_payments
          if @payment.present?
            @payment.each do |payment|
              payment.cancel_by_store!(@current_store) if (payment.respond_to?(:cancel_by_store!) && !payment.cancelled?)
            end
          end
        end

        def create_insurance_token(insurance_token, item)
          InsuranceToken.create(
            token: insurance_token,
            order_id: @order.id,
            amount: checkout_cart_strategy_insurance.cost_by_currency([item])[:ARS].to_f / item.quantity,
            name: @insurance_data.data["name"],
            email: @insurance_data.data["email"],
            installments: @insurance_data.data["installments"],
            gender: @insurance_data.data["gender"],
            birthday: @insurance_data.data["birthday"],
            birth_location: @insurance_data.data["birth_location"],
            nationality: @insurance_data.data["nationality"],
            doc_type: @insurance_data.data["doc_type"],
            doc_number: @insurance_data.data["doc_number"],
            phone: @insurance_data.data["phone"],
            address: @insurance_data.data["address"],
            street_number: @insurance_data.data["street_number"],
            dept: @insurance_data.data["dept"],
            country: @insurance_data.data["country"],
            state: @insurance_data.data["state"],
            city: @insurance_data.data["city"],
            postal_code: @insurance_data.data["postal_code"],
            civil_status: @insurance_data.data["civil_status"],
            profession: @insurance_data.data["profession"],
            political_exposure: @insurance_data.data["political_exposure"],
            reference_id: @insurance_data.data["reference_id"],
            extra_data: @insurance_data.data["extra_data"]) if insurance_token
        end

        def log_payment_fail(payment, checkout_items, exception)
          @import_log ||= Logger.new("#{Rails.root}/log/fail_create_order.log")
          @import_log.info("--------------------------------------------------------")
          if payment.present?
            payment.each.with_index(1) do |pay, index|
              @import_log.info("PAGO #{index}: #{payment}")
            end
          end
          @import_log.info("CHECKOUT ITEMS: #{checkout_items}")
          @import_log.info("EXCEPTION: #{exception.full_message}")
          @import_log.info("REQUEST: #{request.uuid} - #{request.path}: #{ActionDispatch::Http::ParameterFilter.new(Rails.application.config.filter_parameters).filter(request.params)}")
        end

        def check_identity_response!
          validated_doc = answer_tokens = document_payers = []
          if params[:cards].present?
            document_payers = params[:cards].map do |card|
              { doc_number: card.dig(:cardholder,:identification, :number), identity_validation_token: card.dig(:identity_validation_token)}
            end
          else
            # old params
            document_payer = params[:doc_number] || params[:card_holder_identification][:number]
            document_payers = [{ doc_number: document_payer, identity_validation_token: params[:identity_validation_token]}]
          end
          document_payers.uniq!
          document_payers.select! {|payer| payer[:doc_number] != @cart.customer.doc_number }

          return true unless @current_store.strategy_renaper.check_token_at_payment? && document_payers.present?

          document_payers.map do |payer|
            answer_token = validated_doc.include?(payer[:doc_number]) || RenaperAnswer.exists?(answered: true, store_id: @current_store.id, doc_number: payer[:doc_number], token: payer[:identity_validation_token])
            raise IdentityValidationTokenException.new('Debe responder preguntas de identidad antes de realizar el pago') unless answer_token
            validated_doc << payer[:doc_number]
          end

          # TODO Marcar RenaperAnswer como usada/borrada para que no se use un token antiguo, luego de que la compra haya sido completada
          document_payers
        end

        def validate_whitelist
          unless @current_store.whitelist_configuration.present? && @current_store.whitelist_configuration.active
            return
          end
          return if params[:form].nil?

          if dni_active?
            return if dni_authorized?

            data = unauthorized_information

            render json: {
                error: data[:message],
                products: data[:products].map(&:title)
            }, status: 406
          else
            render json: { error: @current_store.whitelist_configuration.unauthorized_user_message }, status: 406
          end
        end

        def dni_active?
          @current_store.whitelist_configuration.user_strategy_class.exists?(params[:form][:doc_number], @current_store)
        end

        def dni_authorized?
          @current_store.whitelist_configuration.validation_strategy_class.authorized?(params[:form][:doc_number], @current_store, @cart)
        end

        def unauthorized_information
          @current_store.whitelist_configuration.validation_strategy_class.build_unauthorized_data(params[:form][:doc_number], @current_store, @cart)
        end

        def response_payment
          {
              success: true,
              order: @order.suborders.flat_map(&:public_id).join(', #'),
              vouchers: @order.items.map { |item| item.coupon&.code }.compact,
              reservation_code: @order.reservation_code.presence,
              current_customer_points: (@current_user.points(with_cache: false) rescue 0)
          }.tap do |resp|
            payment_gateway_data = []
            @payment.each do |pay|
              payment_gateway_data << pay&.gateway_data
            end
            resp.merge!(payment: payment_gateway_data) if @payment.any? { |payment| payment.present? }
            if @payment_points.present?
              resp.merge!({ points_payment: @payment_points&.gateway_data })
            end
          end
        end

        def validate_variant_params
          if params[:items].nil? && @cart.try(:items).blank?
            render json: { error: 'No variants provided' }, status: 406
          end
        end

        # rubocop:disable Metrics/AbcSize
        def find_cart
          return @cart = nil if !@current_user.present? && params[:purchase_id].nil?
          return @cart = @current_user.carts.find_by_purchase_id(params[:purchase_id]) if @current_user.present?
          #unless @current_store.require_login_at_checkout? # unless comentado hasta que extended_warranties envie authtoken
          return @cart = ::Mkp::Cart.find_by_purchase_id(params[:purchase_id]) if params[:purchase_id].present?
          #end

          @cart = if @current_user.present? && params[:purchase_id].nil?
            cart = @current_user.carts.last
            ::Mkp::Order.find_by_purchase_id(cart.try(:purchase_id)).present? ? nil : cart
          end
        end
        # rubocop:enable Metrics/AbcSize

        def ensure_cart_is_there
          cart_not_found if @cart.nil?
        end

        def cart_not_found
          render json: { error: 'You need to build your own cart' }, status: 404
        end

        def remove_current_cart(gateway)
          if gateway == 'todopago'
            Mkp::Payments::RemoveCart.perform_in(5.minutes, @cart.purchase_id)
          else
            @cart.destroy
          end
        end

        def insurance_enabled?
          # Skip insurance for points products
          return false if @checkout_cart&.checkout_items&.any? { |item| item.product.transaction_type == 'points' }
          checkout_cart_strategy_insurance.enabled?
        end

        def checkout_cart_strategy_insurance
          @checkout_cart_strategy_insurance ||= @checkout_cart.store.strategy_insurances(@checkout_cart)
        end

        def insurance_checked_by_user?
          !insurance_params.empty?
        end

        def save_insurance(insurance_data, reference_id = nil)
            insurance_data.data.merge!(reference_id:  reference_id) if reference_id.present?
            insurance_data.data.merge!(date_of_sale: @order.created_at)
            @nacion_tokens_service = NacionTokens.new(NACION_TOKENS_URL, NACION_TOKENS_API_KEY, insurance_data.data) # save to vault service
            @nacion_tokens_service.create_insurance
            if !@nacion_tokens_service.valid?
              ExceptionNotifier.notify_exception(StandardError.new, data: {
                error: 'Error guardando seguro en Vault Service',
                insurance_data: insurance_data.data,
                checkout_cart: @checkout_cart,
                params: params.dup,
                session: session.dup
              })
              return false
            end

            @nacion_tokens_service.response['token']
        end

        def new_insurance_data(insurance_params, item)
          strategy_insurances = checkout_cart_strategy_insurance
          collected_data = insurance_params.dup
          collected_data = collected_data.merge({
            installments: strategy_insurances.installments,
            insurance_amount: strategy_insurances.insurance_amount([item]),
            detailed_model: strategy_insurances.detailed_model([item]),
            amount: strategy_insurances.cost_by_currency([item])[:ARS].to_f / item.quantity,
            extra_data: strategy_insurances.extra_info([item])
          })
          #collected_data = collected_data.merge(card_number: params['card_number'])
          InsuranceData.new(collected_data)
        end

        def proccess_points_payment
          Rails.logger.info("--------------------------------------------LITLE JESUS ---------------------------\n")
          Rails.logger.info(@checkout_cart)
          Rails.logger.info(@checkout_cart.store.has_visa_puntos?)
          if @checkout_cart.store.has_visa_puntos?
            @points_payments = Avenida::Payments::VisaPuntos.collect_payment_by_items(@checkout_cart, @cart, visa_puntos_params, @current_user)
            if @points_payments.any?(&:cancelled?)
              ExceptionNotifier.notify_exception(StandardError.new, data: {
                  error: 'Error procesando pago con puntos',
                  payment: @points_payments,
                  checkout_cart: @checkout_cart,
                  params: params.dup,
                  session: session.dup
              })
              return false
            end
          end

          return true
        end

        def set_tdd_boca_address
          if @current_store.name == 'tddboca'
            build_address(nil, @current_user.addresses.last.id)
            @cart.update_attribute(:delivery_option_id, 'matrix')
          end
        end

        def jubilo_credit_params
          params.permit(:name, :email, :cuil, :phone_number)
        end

        def insurance_params
          params[:insurance_data]&.permit(
            :name, :email, :phone, :birthday, :birth_location, :nationality, :gender, :civil_status, :profession, :doc_type, :doc_number, :address, :street_number, :depth, :country, :state, :city, :postal_code, :political_exposure, :cbu, :floor, :card_number
          ) || {}
        end

        def render_error(error)
          self.response_body = nil # para prevenir el double render en caso de que la trx ya haya dicho que estaba ok y mandó el response
          flash[:alert] = error
          errorMsg = error.is_a?(String) ? error : 'Ha ocurrido un error al intentar procesar su pago'
          render json: { error: errorMsg, errors: [error] }, status: 422
        end

        def render_installments_error(error)
          flash[:alert] = error
          render json: { error: error }, status: 406
        end

        def first_purchase_register
          Pioneer::Blacklist.create(store: @current_store, doc_number: params[:doc_number]) if @current_store.first_purchase_enabled?
        end

        def first_purchase_check(store, doc_number)
          # Verificar first_purchase
          return true unless store.first_purchase_enabled?

          if [FirstPurchase::Validator::FAILURE, FirstPurchase::Validator::WHITELIST_FAILURE, FirstPurchase::Validator::FIRST_PURCHASE_FAILURE].include? first_purchase_validate(store: store, document_number: doc_number)
            return false
          end

          true
        end

        def verify_checkout_cart!
          raise CheckoutCart::Error, 'Carrito duplicado', [] unless ::Mkp::Order.find_by_purchase_id(params['purchase_id']).blank?
          raise CheckoutCart::Error, 'Carrito vacío', [] unless @checkout_cart.items.count > 0
          raise CheckoutCart::Error, 'Monto incorrecto en carrito', [] unless @checkout_cart.total >= 0
          raise CheckoutCart::Error, 'Debes elegir opciones de envío', [] unless @checkout_cart.choosen_delivery_options.present?
        end
      end
    end
  end
end
