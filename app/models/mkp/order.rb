module Mkp
  class Order < ActiveRecord::Base
    has_paper_trail

    extend ::Concerns::WiserPolymorphicBelongsTo

    serialize :data, Hash

    belongs_to :coupon, class_name: 'Coupon::Network'
    belongs_to :customer, polymorphic: true
    belongs_to :store, foreign_key: :store_id, class_name: 'Mkp::Store'
    has_one :info_package, class_name: 'Mkp::Bna::InfoPackage', dependent: :destroy

    listable_through :sale_items, :listables

    # Needed for eager loading purposes.
    wiser_polymorphic_belongs_to :user,
                                 'User',
                                 'customer_id',
                                 'customer_type',
                                 table_name

    wiser_polymorphic_belongs_to :guest,
                                 'Mkp::Guest',
                                 'customer_id',
                                 'customer_type',
                                 table_name

    wiser_polymorphic_belongs_to :buyer,
                                 'Mkp::Customer',
                                 'customer_id',
                                 'customer_type',
                                 table_name

    has_many :payments, as: :sale_item, dependent: :destroy
    has_many :suborders, dependent: :destroy
    has_many :items, class_name: 'OrderItem', through: :suborders
    has_many :shipments, -> { uniq }, through: :suborders
    has_many :direct_shipments, as: :sale_item, dependent: :destroy, class_name: 'Mkp::Shipment'
    has_many :invoices, dependent: :destroy
    has_many :credit_note
    has_one :info_package, class_name: 'Mkp::Bna::InfoPackage', dependent: :destroy
    has_one :customer_reservation_purchases, foreign_key: :mkp_order_id

    attr_accessor :cancel_from_worker

    validates :purchase_id, uniqueness: true

    store :data, accessors: %i[
      affiliate_id
      affiliate_type
      affiliated_at
      affiliate_notified_at
      left_cart
      replica_id
      promotion
    ]

    scope :by_network, lambda { |network|
                         where(mkp_orders: { network: network }) if network.present?
                       }
    scope :with_store_id, ->(store_ids) { where(store_id: [*store_ids]) }
    scope :with_payment_status, ->(payment_status) { where(mkp_payments: { status: payment_status }).joins(:payments) }
    scope :with_shipment_status, ->(shipment_status) { where(mkp_shipments: { status: shipment_status }).joins(:shipments) }
    scope :created_at_gte, ->(reference_time) { where('mkp_orders.created_at >= ?', reference_time) }
    scope :created_at_lt, ->(reference_time) { where('mkp_orders.created_at < ?', reference_time) }
    scope :with_payment_created_at_gte, ->(reference_time) { where('mkp_payments.created_at >= ?', reference_time).joins(:payments) }
    scope :with_payment_created_at_lt, ->(reference_time) { where('mkp_payments.created_at < ?', reference_time).joins(:payments) }
    scope :listables, -> { select_as(store_id: 'store_id', customer_id: 'customer_id', 'customer_type collate utf8_unicode_ci' => :customer_type, 'id_cobis collate utf8_unicode_ci' => :id_cobis) }
    scope :with_payment_status_pending, -> do
      joins("INNER JOIN mkp_payments ON lstble_sale_items.listable_id = mkp_payments.sale_item_id AND lstble_sale_items.listable_type = mkp_payments.sale_item_type")
        .where(mkp_payments: { status: 'pending' }).uniq
    end
    scope :with_payment_status_not_pending_or_expired,  -> do
      joins("LEFT OUTER JOIN mkp_payments ON mkp_payments.sale_item_id = mkp_orders.id")
        .where("mkp_payments.status NOT IN ('pending', 'expired')")
    end
    scope :search_query, lambda { |query|
      query = query.to_s
      return if query.blank?

      terms = query.downcase.split(/\s+/)
      terms = terms.map { |e| (e.tr('*', '%') + '%').gsub(/%+/, '%') }
      num_or_conds = 1
      sql = terms.map { |_term| 'mkp_orders.id like ?' }.join(' OR ')
      guest_ids = Mkp::Guest.search(query).ids
      customer_ids = Mkp::Customer.not_deleted.search_query(query).ids
      where(customer_type: 'Mkp::Guest', customer_id: guest_ids)
        .or(where(customer_type: 'Mkp::Customer', customer_id: customer_ids))
        .or(where(sql, *terms.map { |e| [e] * num_or_conds }.flatten))
    }

    filterrific available_filters: %i[
      with_store_id
      with_payment_status
      with_shipment_status
      created_at_between
      created_at_gte
      created_at_lt
      with_payment_created_at_gte
      with_payment_created_at_lt
      search_query
    ]

    # enumerators
    enum reservation_status: { "not_valid": 0, "active": 1, "used": 2, "canceled": 3 }

    searchable include: :suborders do
      integer :id
      string :network
      integer :store_id
      integer :customer_id
      string :customer_type
      # string :shop_title, multiple: true
      string :payment_status

      string :shipment_status, multiple: true do
        shipments.map(&:status).last
      end

      text :order_id do
        # Keeping this because of https://github.com/sunspot/sunspot/issues/331
        # and until the PR https://github.com/sunspot/sunspot/pull/784 is merged
        # or resolved in anyway
        attributes['id'].to_s
      end

      text :gp_sku do
        items.map(&:variant).map(&:gp_sku).join(' ')
      end

      text :customer_data do
        [customer.full_name, customer.email].join(' ') if customer.present?
      end

      boolean :fulfilled

      double :total, stored: true
      text :title
      time :created_at, trie: true

      string :tracking_numbers, multiple: true do
        if have_shipments?
          shipments.flat_map { |s| s.labels.active }
                   .map(&:tracking_number)
                   .uniq
                   .compact
        end
      end

      time :paid_at, trie: true do
        if payment.present? && payment.collected_at.present?
          payment.collected_at
        else
          created_at unless balance_due?
        end
      end
    end

    def payment
      if payments.count == 1
        payments.first
      elsif payments.count > 1
        payments.where.not(gateway: 'LoyaltyBna').last
      end
    end

    def any_shop?
      shops = suborders.map(&:shop).map(&:title)
      Mkp::Shop.where(title: shops).any?
    end

    def total_iva
      items.inject(0) { |total, item| total + item.total_iva }
    end

    def have_shipments?
      shipments.present?
    end

    def coupons_discount
      return 0 unless has_coupon?

      suborders.to_a.sum(&:coupon_discount)
    end

    def promotion_discount
      return 0 if promotion.blank?

      promotion[:discount]
    end

    def pickeable?
      suborders.all? { |s| s.shipment.is_pickup? }
    end

    def has_coupon?
      coupon.present? || suborders.detect(&:has_coupon?)
    end

    def have_taxes?
      taxes > 0
    end

    def shipments_cost
      shipments.to_a.select {|shipment| shipment.charged_amount != nil }.sum(&:charged_amount)
    end

    def shipments_bonifications
      shipments.to_a.sum(&:bonified_amount)
    end

    def subtotal
      suborders.to_a.sum(&:subtotal)
    end

    def points
      self['points'] != 0 ? self['points'] : items.sum(:points)
    end

    def sp_subtotal_points
      points
    end

    def subtotal_with_shipment
      subtotal + shipments_cost
    end

    def bonified_amount_total
      bonified_amount + coupons_discount
    end

    def taxes
      suborders.sum(:taxes)
    end

    def net_total
      total.to_f -
        shipments_cost.to_f -
        taxes.to_f +
        coupon_discount.to_f +
        promotion_discount.to_f
    end

    def total
      total_without_discount - coupon_discount.to_f - promotion_discount.to_f - bonified_amount.to_f
    end

    def total_points
      [payments.where(gateway: %w[VisaPuntos SystemPoint LoyaltyBna]).sum(:collected_amount).to_i,
       self.points].max
    end

    def total_without_discount
      total_without_shipment + shipments_cost
    end

    def total_without_shipment
      suborders.to_a.sum(0, &:total_without_shipment)
    end

    def total_without_points
      payments.where.not(gateway: %w[VisaPuntos SystemPoint LoyaltyBna])
              .sum(:collected_amount)
    end

    def total_spended_points
      self.points
    end

    def total_with_coef
      return 0 unless payments.any? { |p| p.gateway_data[:installment_data] }

      coef = payments.map { |payment| payment.gateway_data[:installment_data][:coef].to_i }

      coef.first
    end

    def coupon_discount
      self[:coupon_discount] || 0
    end

    def total_products
      suborders.to_a.sum(&:total_products)
    end

    def avg_of_discount_applied
      return 100 if total.zero? && net_total.zero?
      desc = total_without_discount - total
      (desc * 100 / net_total).round
    rescue StandardError
      100
    end

    def is_omitible_invoice?
      suborders.all? { |suborder| suborder.shop.fc? && !suborder.fulfilled_by_gp? }
    end

    def coupon_code
      return nil unless has_coupon?

      coupon.try(:code) || suborders.map(&:coupon).uniq.compact.first.try(:code)
    end

    def currency_code
      items.last.currency.identifier.upcase
    end

    def balance_due?
      total > 0
    end

    def is_paid?
      (payment.present? && ['collected', 'pending', 'expired'].include?(payment.status)) || coupon_discount >= total_without_discount
    end

    def payment_status(suborder: nil)
      return 'error' unless payment.present?
      return payment.suborder_status(suborder) if payment.present? && suborder.present?
      return payment.status if payment.present?

      coupon.present? ? 'coupon_collected' : 'unknown'
    end

    def shipment_is_pickup?
      return false unless shipments.any?

      shipments.first.is_pickup?
    end

    def update_address(params)
      customer.update(email: params[:email]) if params[:email].present?

      if shipments.any?
        address = suborders.first.shipment.destination_address
        address.address = params[:address] if params[:address].present?
        address.address_2 = params[:address_2] if params[:address_2].present?
        address.street_number = params[:street_number] if params[:street_number].present?

        suborders.first.shipment.update(destination_address: address)
      end

      data = self.data.merge!({ 'address_update': params[:reason] })
      update(data: data) if params[:reason].present?
    end

    def generate_brukman_order
      # BRUKMAN_ID=956
      return unless is_paid?

      suborder = suborders.find { |suborder| suborder.shop.id.eql?(956) }
      if suborder.present?
        Mkp::Integration::GeneratePurchaseByBrukmanWorker.perform_async(suborder.id)
      end
    end

    def credit_note_generate
      credit_notes.create(gateway: 'Colppy')
      credit_notes.last.generate!(self, nil) if credit_notes.any?
    end

    def generate_coupon(reason)
      Mkp::Coupon::Network.create(
        type: 'Mkp::Coupon::Network',
        code: "#{id}#{Mkp::Coupon::Network.random_coupon}",
        description: "Cupon por cancelación de orden #{id} - Motivo: #{reason}",
        store_id: store_id,
        policy: 'value',
        minimum_value: 0,
        amount: total,
        total_available: 1,
        starts_at: Time.zone.now,
        expires_at: Time.zone.now + 90.days,
        apply_on_sale: 1,
        network: 'AR'
      )
    end

    def shop_title
      return '' unless any_shop?

      suborders.map(&:shop).map(&:title)
    end

    def fulfilled
      suborders.any?(&:fulfilled_by_gp?)
    end

    def has_visa_puntos_payment?
      payments.any? { |p| p.gateway == 'VisaPuntos' }
    end

    def items_count
      items.map(&:quantity).sum
    end

    def customer_uuid
      customer.present? ? customer.uuid : nil
    end

    def customer_full_name
      customer.present? ? customer.full_name : 'N/A'
    end

    def is_cancellable?
      return false unless store.allow_orders_cancellation
      return false unless suborders.pluck(:refunded).none?

      if shipments.any?
        cancellable_statuses = %w[unfulfilled in_process not_delivered delivered shipped pending]
        shipments.all? { |s| cancellable_statuses.include?(s.status) } && !(is_changed? || is_refunded?)
      else
        true
      end
    end

    def is_refundable?
      suborders.all?(&:is_refundable?)
    end

    def is_changed?
      shipments.any? { |s| s.shipment_kind == 'exchange_refund' || s.shipment_kind == 'exchange_change' }
    end

    def is_refunded?
      shipments.any? { |s| s.shipment_kind == 'refund' }
    end

    def target_item
      nil
    end

    def shipment
      shipments.last
    end

    def gateway
      nil
    end

    def miles
      nil
    end

    def shops
      suborders.map(&:shop)
    end
  end
end
