module Pages
  module Components
    class Cols4VariantsV1 < Cols4V1
      include VariantComponent

      ALLOWED_KEYS = [
        :view_more_url,
        items: [:variant_id, :variant_product_sku]
      ].freeze

      N_OF_ITEMS = 8

      attr_accessor :store

      def featured_variants(store)
        @store = store

        # Obtener IDs únicos para evitar duplicaciones
        unique_ids = featured_variants_ids.uniq

        # Si no hay IDs válidos, devolver array vacío
        return [] if unique_ids.empty?

        # No usar memoización para evitar problemas de cache
        variants = Mkp::Variant.includes(variant_includes).where('mkp_variants.id IN (?)', unique_ids)

        # Filtrar para asegurar que no hay productos duplicados
        unique_product_variants = []
        seen_product_ids = Set.new

        variants.each do |variant|
          unless seen_product_ids.include?(variant.product_id)
            unique_product_variants << variant
            seen_product_ids << variant.product_id
          end
        end

        # Log para debugging
        configured_count = setup[:items].count { |item| item['variant_id'].present? }
        found_count = unique_product_variants.size

        if found_count < configured_count
          Rails.logger.warn "Component Cols4VariantsV1 ID #{id}: #{configured_count} variantes configuradas, solo #{found_count} válidas encontradas. Algunas variantes pueden estar eliminadas o sin stock."
        end

        return unique_product_variants
      end

      # Método para limpiar el cache específico de este componente
      def clear_cache!
        cache_key = ['component', 'variants_c4v2', self.id, self.featured_variants(@store), self.updated_at]
        Rails.cache.delete(cache_key)
      end

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do |index|
            self.items << {
              variant_id: nil,
              variant_product_sku: nil
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def variant_includes
        [:picture, product: [:shop, :manufacturer, :category]]
      end
    end
  end
end
