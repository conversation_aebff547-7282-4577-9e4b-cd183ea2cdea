module Pages
  module Components
    class Cols4VariantsV1 < Cols4V1
      include VariantComponent

      ALLOWED_KEYS = [
        :view_more_url,
        items: [:variant_id, :variant_product_sku]
      ].freeze

      N_OF_ITEMS = 8

      attr_accessor :store

      def featured_variants(store)
        @store = store

        # Obtener IDs únicos para evitar duplicaciones
        unique_ids = featured_variants_ids.uniq

        # Si no hay IDs válidos, devolver array vacío
        return [] if unique_ids.empty?

        # No usar memoización para evitar problemas de cache
        variants = Mkp::Variant.includes(variant_includes).where('mkp_variants.id IN (?)', unique_ids).to_a

        # Debug: mostrar variantes antes del filtro
        Rails.logger.info "Component #{id}: #{variants.count} variantes encontradas antes del filtro"
        variants.each do |v|
          Rails.logger.info "  - Variant ID #{v.id}, Product ID #{v.product_id}: #{v.title}"
        end

        # Filtrar para asegurar que no hay productos duplicados
        # Usar un enfoque más explícito para debugging
        unique_product_variants = []
        seen_product_ids = Set.new

        variants.each do |variant|
          if seen_product_ids.include?(variant.product_id)
            Rails.logger.info "  DUPLICADO DETECTADO: Variant ID #{variant.id} (Product ID #{variant.product_id}) - SALTANDO"
          else
            Rails.logger.info "  AGREGANDO: Variant ID #{variant.id} (Product ID #{variant.product_id})"
            unique_product_variants << variant
            seen_product_ids << variant.product_id
          end
        end

        # Log para debugging
        configured_count = setup[:items].count { |item| item['variant_id'].present? }
        found_count = unique_product_variants.size

        Rails.logger.info "Component #{id}: #{configured_count} configuradas, #{found_count} únicas después del filtro"

        if found_count < configured_count
          Rails.logger.warn "Component Cols4VariantsV1 ID #{id}: #{configured_count} variantes configuradas, solo #{found_count} válidas encontradas. Algunas variantes pueden estar eliminadas o sin stock."
        end

        return unique_product_variants
      end

      # Método para limpiar el cache específico de este componente
      def clear_cache!
        cache_key = ['component', 'variants_c4v2', self.id, self.featured_variants(@store), self.updated_at]
        Rails.cache.delete(cache_key)
      end

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do |index|
            self.items << {
              variant_id: nil,
              variant_product_sku: nil
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def variant_includes
        [:picture, product: [:shop, :manufacturer, :category]]
      end
    end
  end
end
