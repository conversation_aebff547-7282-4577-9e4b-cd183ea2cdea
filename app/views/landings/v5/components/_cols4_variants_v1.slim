- cache_key = ['component', 'variants_c4v2_fixed', component.id, component.featured_variants_ids, component.updated_at]
= cache cache_key, expires_in: 15.minutes do
  .featured-variants-cmpnt
    - if component.title.present?
      h2.component-title = component.title
    .scroll-wrapper
      = render partial: 'partials/v5/variants/list',
                locals: { variants: component.featured_variants(@current_store), list_name: "landing-featured-products-#{component.id}" }
    - component.setup[:view_more_url] ||= nil
    - url = component.setup[:view_more_url]
    -if url.present?
      = render partial: 'partials/v5/load_more_wide',
               locals: { style: 'white-blue', attrs: { title: t('.see_more'), href: url.present? ? url : mkp_catalog_path } }
