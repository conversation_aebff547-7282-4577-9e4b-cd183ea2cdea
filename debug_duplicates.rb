#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== DEBUG DUPLICADOS RESTANTES ==="

# Buscar el componente específico
component = Pages::Components::Cols4VariantsV1.find(34)
store = Mkp::Store.first

# Limpiar variables de instancia para forzar recálculo
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)
component.instance_variable_set(:@featured_variants, nil)
component.instance_variable_set(:@store, store)

puts "Componente ID: #{component.id}"

puts "\n=== FEATURED VARIANTS IDS ==="
featured_ids = component.featured_variants_ids
puts "Featured IDs: #{featured_ids}"
puts "Featured count: #{featured_ids.length}"

puts "\n=== VERIFICAR CADA ID ==="
featured_ids.each_with_index do |id, index|
  begin
    variant = Mkp::Variant.find(id)
    puts "#{index + 1}. ID #{id}: Product ID #{variant.product_id} - #{variant.title}"
  rescue => e
    puts "#{index + 1}. ID #{id}: ERROR - #{e.message}"
  end
end

puts "\n=== FEATURED VARIANTS DESPUÉS DEL FILTRO ==="
featured_variants = component.featured_variants(store)
puts "Variantes después del filtro: #{featured_variants.count}"

featured_variants.each_with_index do |variant, index|
  puts "#{index + 1}. ID: #{variant.id} - Product ID: #{variant.product_id} - #{variant.title}"
end

puts "\n=== VERIFICAR DUPLICADOS POR PRODUCT_ID ==="
product_ids = featured_variants.map(&:product_id)
puts "Product IDs: #{product_ids}"
puts "Product IDs únicos: #{product_ids.uniq}"

if product_ids.size != product_ids.uniq.size
  puts "⚠️  TODAVÍA HAY DUPLICADOS POR PRODUCT_ID!"
  duplicates = product_ids.group_by(&:itself).select { |k, v| v.size > 1 }
  duplicates.each do |product_id, occurrences|
    puts "   Product ID #{product_id} aparece #{occurrences.size} veces:"
    same_product_variants = featured_variants.select { |v| v.product_id == product_id }
    same_product_variants.each do |variant|
      puts "     - Variant ID #{variant.id}: #{variant.title}"
    end
  end
else
  puts "✅ No hay duplicados por Product ID"
end

puts "\n=== VERIFICAR SI EL FILTRO SE ESTÁ APLICANDO ==="
# Simular el filtro manualmente
puts "Simulando filtro manual:"
unique_product_variants = []
seen_product_ids = Set.new

featured_variants.each_with_index do |variant, index|
  puts "Procesando variant #{variant.id} (Product ID: #{variant.product_id})"
  
  if seen_product_ids.include?(variant.product_id)
    puts "  -> DUPLICADO DETECTADO, saltando"
  else
    puts "  -> AGREGANDO (nuevo product_id)"
    unique_product_variants << variant
    seen_product_ids << variant.product_id
  end
end

puts "\nResultado del filtro manual:"
puts "Variantes únicas: #{unique_product_variants.count}"
unique_product_variants.each_with_index do |variant, index|
  puts "#{index + 1}. ID: #{variant.id} - Product ID: #{variant.product_id} - #{variant.title}"
end

puts "\n=== FIN DEBUG ==="
