#!/usr/bin/env ruby

# Script para debuggear el componente de variantes
# Ejecutar con: bundle exec ruby debug_component.rb

require_relative 'config/environment'

puts "=== DEBUG COMPONENT VARIANTS ==="

# Buscar componentes Cols4VariantsV1
components = Pages::Components::Cols4VariantsV1.all

if components.empty?
  puts "No se encontraron componentes Cols4VariantsV1"
  exit
end

components.each do |component|
  puts "\n--- Componente ID: #{component.id} ---"
  puts "Setup items count: #{component.setup[:items].size}"
  
  puts "\nSetup items:"
  component.setup[:items].each_with_index do |item, index|
    puts "  #{index + 1}. variant_product_sku: '#{item['variant_product_sku']}'"
    puts "     variant_id: '#{item['variant_id']}'"
  end
  
  # Verificar product_skus únicos
  product_skus = component.setup[:items].map { |hash| hash['variant_product_sku'] }.compact.reject(&:blank?)
  unique_product_skus = product_skus.uniq
  
  puts "\nProduct SKUs encontrados: #{product_skus.size}"
  puts "Product SKUs únicos: #{unique_product_skus.size}"
  
  if product_skus.size != unique_product_skus.size
    puts "⚠️  HAY DUPLICADOS EN EL SETUP!"
    duplicates = product_skus.group_by(&:itself).select { |k, v| v.size > 1 }
    duplicates.each do |sku, occurrences|
      puts "   '#{sku}' aparece #{occurrences.size} veces"
    end
  end
  
  puts "\nProduct SKUs únicos:"
  unique_product_skus.each do |sku|
    puts "  - #{sku}"
  end
  
  # Probar retrieve_fixed_variants
  store = Mkp::Store.first
  component.instance_variable_set(:@store, store)
  
  begin
    fixed_variants = component.send(:retrieve_fixed_variants)
    puts "\nFixed variants encontradas: #{fixed_variants&.count || 0}"
    
    if fixed_variants&.any?
      fixed_variants.each do |variant|
        puts "  - Variant ID #{variant.id}: #{variant.title} (SKU: #{variant.sku})"
      end
    end
    
    # Probar featured_variants_ids
    featured_ids = component.featured_variants_ids
    puts "\nFeatured variants IDs: #{featured_ids}"
    puts "Featured variants IDs únicos: #{featured_ids.uniq}"

    if featured_ids.size != featured_ids.uniq.size
      puts "⚠️  HAY DUPLICADOS EN FEATURED_VARIANTS_IDS!"
    end

    # Probar featured_variants method
    featured_variants = component.featured_variants(store)
    puts "\nFeatured variants count: #{featured_variants.count}"
    puts "Featured variants details:"
    featured_variants.each_with_index do |variant, index|
      puts "  #{index + 1}. ID: #{variant.id} - #{variant.title}"
    end

    # Verificar si hay duplicados en las variantes finales
    variant_ids = featured_variants.map(&:id)
    if variant_ids.size != variant_ids.uniq.size
      puts "⚠️  HAY DUPLICADOS EN LAS VARIANTES FINALES!"
      duplicates = variant_ids.group_by(&:itself).select { |k, v| v.size > 1 }
      duplicates.each do |variant_id, occurrences|
        puts "   Variant ID #{variant_id} aparece #{occurrences.size} veces"
      end
    else
      puts "✅ No hay duplicados en las variantes finales"
    end
    
  rescue => e
    puts "Error al obtener fixed_variants: #{e.message}"
    puts e.backtrace.first(3)
  end
  
  break # Solo probar el primer componente
end

puts "\n=== FIN DEBUG ==="
