#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== APLICAR FIX FINAL PARA DUPLICADOS ==="

# Limpiar cache completamente
Rails.cache.clear
puts "✅ Cache de Rails limpiado"

# Actualizar timestamp del componente para invalidar cache
component = Pages::Components::Cols4VariantsV1.find(34)
component.touch
puts "✅ Componente actualizado (timestamp: #{component.updated_at})"

# Limpiar variables de instancia
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)
component.instance_variable_set(:@featured_variants, nil)
puts "✅ Variables de instancia limpiadas"

puts "\n=== PROBAR NUEVA LÓGICA ==="
store = Mkp::Store.first
component.instance_variable_set(:@store, store)

# Probar featured_variants_ids
featured_ids = component.featured_variants_ids
puts "Featured IDs: #{featured_ids}"
puts "Featured count: #{featured_ids.length}"

# Verificar que no hay duplicados por product_id
if featured_ids.any?
  variants = Mkp::Variant.where(id: featured_ids)
  product_ids = variants.map(&:product_id)
  puts "Product IDs: #{product_ids}"
  puts "Product IDs únicos: #{product_ids.uniq}"
  
  if product_ids.size != product_ids.uniq.size
    puts "⚠️  TODAVÍA HAY DUPLICADOS EN featured_variants_ids"
  else
    puts "✅ featured_variants_ids no tiene duplicados por product_id"
  end
end

# Probar featured_variants
featured_variants = component.featured_variants(store)
puts "\nFeatured variants count: #{featured_variants.count}"

if featured_variants.any?
  product_ids = featured_variants.map(&:product_id)
  puts "Product IDs finales: #{product_ids}"
  puts "Product IDs únicos finales: #{product_ids.uniq}"
  
  if product_ids.size != product_ids.uniq.size
    puts "⚠️  TODAVÍA HAY DUPLICADOS EN featured_variants"
  else
    puts "✅ featured_variants no tiene duplicados por product_id"
  end
end

puts "\n=== FIX APLICADO ==="
puts "Reinicia el servidor y verifica el frontend."
puts "Ahora deberías ver solo 1 producto único en lugar de 3 repetidos."
