#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== PROBAR NUEVA LÓGICA SIN VARIANTES ALEATORIAS ==="

# Buscar el componente específico
component = Pages::Components::Cols4VariantsV1.find(34)
store = Mkp::Store.first

puts "Componente ID: #{component.id}"

# Limpiar variables de instancia para forzar recálculo
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)
component.instance_variable_set(:@featured_variants, nil)
component.instance_variable_set(:@store, store)

puts "\n=== VARIANTES CONFIGURADAS ==="
configured_variants = 0
component.setup[:items].each_with_index do |item, index|
  if item['variant_id'].present?
    configured_variants += 1
    puts "#{index + 1}. variant_id: #{item['variant_id']}"
  end
end
puts "Total configuradas: #{configured_variants}"

puts "\n=== FIXED VARIANTS IDS (sin aleatorias) ==="
fixed_ids = component.send(:fixed_variants_ids)
puts "Fixed IDs: #{fixed_ids}"
puts "Fixed count: #{fixed_ids.length}"

puts "\n=== RANDOM VARIANTS IDS (debería estar vacío) ==="
random_ids = component.send(:random_variants_ids)
puts "Random IDs: #{random_ids}"
puts "Random count: #{random_ids.length}"

puts "\n=== FEATURED VARIANTS IDS FINAL ==="
featured_ids = component.featured_variants_ids
puts "Featured IDs: #{featured_ids}"
puts "Featured count: #{featured_ids.length}"

puts "\n=== FEATURED VARIANTS FINAL ==="
featured_variants = component.featured_variants(store)
puts "Variantes encontradas: #{featured_variants.count}"

if featured_variants.any?
  featured_variants.each_with_index do |variant, index|
    puts "#{index + 1}. ID: #{variant.id} - #{variant.title}"
  end
else
  puts "⚠️  No se encontraron variantes válidas"
end

puts "\n=== RESUMEN ==="
puts "Configuradas: #{configured_variants}"
puts "Válidas encontradas: #{featured_variants.count}"
puts "Espacios vacíos: #{8 - featured_variants.count}" if featured_variants.count < 8

if featured_variants.count < configured_variants
  puts "⚠️  Algunas variantes configuradas están eliminadas o no tienen stock"
else
  puts "✅ Todas las variantes configuradas son válidas"
end

puts "\n=== FIN PRUEBA ==="
