namespace :avenida do
  namespace :cache do
    desc 'Clear cache for all variant components to fix duplication issues'
    task clear_variants_components: :environment do
      puts "Clearing cache for all variant components..."

      # Limpiar TODO el cache de Rails
      Rails.cache.clear
      puts "Rails cache cleared completely"

      # Limpiar cache de Redis si está disponible
      if defined?($redis)
        puts "Clearing Redis cache..."
        keys = $redis.keys("cache:*")
        if keys.any?
          $redis.del(keys)
          puts "Deleted #{keys.size} Redis cache keys"
        else
          puts "No Redis cache keys found"
        end
      end

      # Limpiar variables de instancia en componentes
      Pages::Components::Cols4VariantsV1.all.each do |component|
        component.instance_variable_set(:@fixed_variants_ids, nil)
        component.instance_variable_set(:@featured_variants, nil)
        component.instance_variable_set(:@search_variants_ids, nil)
        puts "Cleared instance variables for Cols4VariantsV1 component ID: #{component.id}"
      end

      Pages::Components::Cols2VariantsV1.all.each do |component|
        component.instance_variable_set(:@fixed_variants_ids, nil)
        component.instance_variable_set(:@featured_variants, nil)
        component.instance_variable_set(:@search_variants_ids, nil)
        puts "Cleared instance variables for Cols2VariantsV1 component ID: #{component.id}"
      end

      puts "Cache clearing completed!"
      puts "Please restart your server to ensure all changes take effect."
    end
    
    desc 'Clear cache for a specific component by ID'
    task :clear_component_cache, [:component_id] => :environment do |t, args|
      component_id = args[:component_id]
      
      if component_id.blank?
        puts "Please provide a component ID: rake avenida:cache:clear_component_cache[123]"
        exit
      end
      
      # Intentar encontrar el componente en ambos tipos
      component = Pages::Components::Cols4VariantsV1.find_by(id: component_id) ||
                  Pages::Components::Cols2VariantsV1.find_by(id: component_id)
      
      if component.nil?
        puts "Component with ID #{component_id} not found"
        exit
      end
      
      # Limpiar cache específico del componente
      cache_patterns = [
        ['component', 'variants_c4v2', component.id, '*'],
        ['component', 'variants_c2v1', component.id, '*'],
        ['component', 'variants_c4v2', component.id, component.updated_at],
        ['component', 'variants_c2v1', component.id, component.updated_at]
      ]
      
      cache_patterns.each do |pattern|
        Rails.cache.delete(pattern)
      end
      
      puts "Cache cleared for component ID: #{component_id} (#{component.class.name})"
    end
  end
end
