namespace :avenida do
  namespace :cache do
    desc 'Clear cache for all variant components to fix duplication issues'
    task clear_variants_components: :environment do
      puts "Clearing cache for all variant components..."
      
      # Limpiar cache de componentes cols4_variants_v1
      Pages::Components::Cols4VariantsV1.all.each do |component|
        cache_patterns = [
          ['component', 'variants_c4v2', component.id, '*'],
          ['component', 'variants_c4v2', component.id, component.updated_at]
        ]
        
        cache_patterns.each do |pattern|
          Rails.cache.delete(pattern)
        end
        
        puts "Cleared cache for Cols4VariantsV1 component ID: #{component.id}"
      end
      
      # Limpiar cache de componentes cols2_variants_v1
      Pages::Components::Cols2VariantsV1.all.each do |component|
        cache_patterns = [
          ['component', 'variants_c2v1', component.id, '*'],
          ['component', 'variants_c2v1', component.id, component.updated_at]
        ]
        
        cache_patterns.each do |pattern|
          Rails.cache.delete(pattern)
        end
        
        puts "Cleared cache for Cols2VariantsV1 component ID: #{component.id}"
      end
      
      # Limpiar todo el cache relacionado con variantes usando patrones
      cache_keys_to_delete = []
      
      # Buscar todas las claves de cache que contengan 'variants'
      if Rails.cache.respond_to?(:redis)
        cache_keys_to_delete = Rails.cache.redis.keys("cache:*variants*")
      elsif defined?($redis)
        cache_keys_to_delete = $redis.keys("cache:*variants*")
      end
      
      cache_keys_to_delete.each do |key|
        # Remover el prefijo 'cache:' si existe
        clean_key = key.gsub(/^cache:/, '')
        Rails.cache.delete(clean_key)
        puts "Deleted cache key: #{clean_key}"
      end
      
      puts "Cache clearing completed!"
      puts "Total cache keys deleted: #{cache_keys_to_delete.size}"
    end
    
    desc 'Clear cache for a specific component by ID'
    task :clear_component_cache, [:component_id] => :environment do |t, args|
      component_id = args[:component_id]
      
      if component_id.blank?
        puts "Please provide a component ID: rake avenida:cache:clear_component_cache[123]"
        exit
      end
      
      # Intentar encontrar el componente en ambos tipos
      component = Pages::Components::Cols4VariantsV1.find_by(id: component_id) ||
                  Pages::Components::Cols2VariantsV1.find_by(id: component_id)
      
      if component.nil?
        puts "Component with ID #{component_id} not found"
        exit
      end
      
      # Limpiar cache específico del componente
      cache_patterns = [
        ['component', 'variants_c4v2', component.id, '*'],
        ['component', 'variants_c2v1', component.id, '*'],
        ['component', 'variants_c4v2', component.id, component.updated_at],
        ['component', 'variants_c2v1', component.id, component.updated_at]
      ]
      
      cache_patterns.each do |pattern|
        Rails.cache.delete(pattern)
      end
      
      puts "Cache cleared for component ID: #{component_id} (#{component.class.name})"
    end
  end
end
