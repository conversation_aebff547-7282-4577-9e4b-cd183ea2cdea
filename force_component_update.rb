#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== FORZAR ACTUALIZACIÓN DEL COMPONENTE ==="

# Buscar el componente específico
component = Pages::Components::Cols4VariantsV1.find(34)

puts "Componente ID: #{component.id}"
puts "Updated at antes: #{component.updated_at}"

# Forzar actualización del timestamp para invalidar cache
component.touch

puts "Updated at después: #{component.updated_at}"

# Limpiar variables de instancia
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)
component.instance_variable_set(:@featured_variants, nil)

puts "Variables de instancia limpiadas"

# Limpiar cache de Rails
Rails.cache.clear
puts "Cache de Rails limpiado"

puts "=== COMPONENTE ACTUALIZADO ==="
puts "El frontend debería mostrar ahora los productos únicos correctos."
puts "Si el problema persiste, actualiza las variantes en el backoffice."
