- payment = suborder.payment
- points = suborder.subtotal_points
- coupon = suborder.coupon

.div.text-center
  h3=t('.title')
  - if suborder.have_taxes?
    .taxes=t('.taxes', amount: number_to_currency(suborder.taxes, precision: 2))
  .details
    .row
      - if payment.present? && payment.gateway != 'LoyaltyBna'
          div class=(payment_column_class(suborder)) style="padding-bottom: 1em;"
            div class='padding-top-1'
            h4= suborder.order.payments.map {|p| p.gateway }.uniq.map {|p| t(p, scope: 'payment-gateways') }.join(' + ')
            h3.subtotal=  "#{number_to_currency(suborder.total, precision: 2)}"
            - payment_status = payment.suborder_status(suborder)
            span.payment_status class="#{payment_status}" = t(".#{payment_status}")
      - if points > 0
        div class=(payment_column_class(suborder))
          h4= t('.points')
          h3.subtotal= points
          - if suborder.order.payments.present?
            - payment_status = suborder.order.payments.with_points.first.suborder_status(suborder)
            span.payment_status class="#{payment_status}" = t(".#{payment_status}")
          - else
            span.payment_status class="#{payment_status}" = t(".collected")

      - if suborder.coupon_discount.positive?
        div class=(payment_column_class(suborder))
          h4= t('.coupon')
          span = t(".ticket", code: coupon.code)

      - if @customer_reservation.present?
        .row
          .medium-6.columns
          h4 = t('.loan').upcase
