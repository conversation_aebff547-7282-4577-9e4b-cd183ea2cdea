- #VisaPuntos payment esta asociado a suborder.payments, Decidir no.
- #grouped_payment en realidad muestra en una tabla todos los payments excepto VisaPuntos
- suborder_payments = order.payments.count == 1 ? suborder.order.payments : suborder.payments + suborder.order.payments.where.not(gateway: "VisaPuntos")
table.table.mb-0
  - payments_collected_count = suborder.order.payments.where.not(gateway: "VisaPuntos").size
  - if payments_collected_count == 1 && suborder_payments.first.gateway != 'LoyaltyBna'
    - suborder_payments.each do |payment|
      = render partial: 'pioneer/orders/payment', locals: { payments: suborder_payments, payment: payment, suborder: suborder }
  - else
    = render partial: 'pioneer/orders/grouped_payment', locals: { payments: suborder_payments, suborder: suborder }
