#!/usr/bin/env ruby

require_relative 'config/environment'

puts "=== APLICAR NUEVA LÓGICA ==="

# Limpiar cache completamente
Rails.cache.clear
puts "✅ Cache de Rails limpiado"

# Actualizar timestamp del componente para invalidar cache
component = Pages::Components::Cols4VariantsV1.find(34)
component.touch
puts "✅ Componente actualizado (timestamp: #{component.updated_at})"

# Limpiar variables de instancia
component.instance_variable_set(:@fixed_variants_ids, nil)
component.instance_variable_set(:@search_variants_ids, nil)
component.instance_variable_set(:@featured_variants, nil)
puts "✅ Variables de instancia limpiadas"

puts "\n=== NUEVA LÓGICA APLICADA ==="
puts "Ahora el componente:"
puts "- Solo mostrará las variantes configuradas que existen"
puts "- NO rellenará con variantes aleatorias"
puts "- Dejará espacios vacíos si hay variantes eliminadas"
puts "- Mostrará solo 1 variante válida en lugar de 8 aleatorias"

puts "\nReinicia el servidor y verifica el frontend."
puts "Deberías ver solo 1 producto (Tostadora Yelmo) en lugar de 8."
