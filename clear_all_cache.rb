#!/usr/bin/env ruby

# Script para limpiar completamente el cache
# Ejecutar con: bundle exec ruby clear_all_cache.rb

require_relative 'config/environment'

puts "Limpiando todo el cache..."

# Limpiar cache de Rails
Rails.cache.clear

# Limpiar cache de Redis si está disponible
if defined?($redis)
  puts "Limpiando cache de Redis..."
  keys = $redis.keys("cache:*")
  if keys.any?
    $redis.del(keys)
    puts "Eliminadas #{keys.size} claves de cache"
  else
    puts "No se encontraron claves de cache en Redis"
  end
end

puts "Cache limpiado completamente!"
puts "Reinicia el servidor para asegurar que los cambios se apliquen."
